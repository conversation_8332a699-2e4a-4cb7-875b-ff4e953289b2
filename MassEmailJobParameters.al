// Table to store parameters for mass email background jobs
table 50112 "Mass Email Job Parameters"
{
    Caption = 'Mass Email Job Parameters';
    DataClassification = CustomerContent;

    fields
    {
        field(1; "Entry No."; Integer)
        {
            Caption = 'Entry No.';
            AutoIncrement = true;
        }
        field(2; "Job Queue Entry ID"; Guid)
        {
            Caption = 'Job Queue Entry ID';
            TableRelation = "Job Queue Entry".ID;
        }
        field(3; "Salesperson Code"; Code[20])
        {
            Caption = 'Salesperson Code';
            TableRelation = "Salesperson/Purchaser";
        }
        field(4; "Campaign No."; Code[20])
        {
            Caption = 'Campaign No.';
            TableRelation = Campaign;
        }
        field(5; "Email Subject"; Text[250])
        {
            Caption = 'Email Subject';
        }
        field(6; "Email Body"; Blob)
        {
            Caption = 'Email Body';
        }
        field(7; "Created By"; Code[50])
        {
            Caption = 'Created By';
        }
        field(8; "Created Date Time"; DateTime)
        {
            Caption = 'Created Date Time';
        }
        field(9; "Total Recipients"; Integer)
        {
            Caption = 'Total Recipients';
        }
        field(10; "Emails Sent"; Integer)
        {
            Caption = 'Emails Sent';
        }
        field(11; "Status"; Enum "Mass Email Job Status")
        {
            Caption = 'Status';
        }
        field(12; "Error Message"; Text[250])
        {
            Caption = 'Error Message';
        }
        field(13; "Attachment Count"; Integer)
        {
            Caption = 'Attachment Count';
        }
    }

    keys
    {
        key(PK; "Entry No.")
        {
            Clustered = true;
        }
        key(JobQueueKey; "Job Queue Entry ID")
        {
        }
    }

    procedure SetEmailBody(EmailBodyText: Text)
    var
        OutStream: OutStream;
    begin
        "Email Body".CreateOutStream(OutStream, TEXTENCODING::UTF8);
        OutStream.WriteText(EmailBodyText);
    end;

    procedure GetEmailBody(): Text
    var
        InStream: InStream;
        EmailBodyText: Text;
    begin
        CalcFields("Email Body");
        "Email Body".CreateInStream(InStream, TEXTENCODING::UTF8);
        InStream.ReadText(EmailBodyText);
        exit(EmailBodyText);
    end;
}

// Enum for mass email job status
enum 50112 "Mass Email Job Status"
{
    Extensible = true;

    value(0; Queued)
    {
        Caption = 'Queued';
    }
    value(1; "In Progress")
    {
        Caption = 'In Progress';
    }
    value(2; Completed)
    {
        Caption = 'Completed';
    }
    value(3; Error)
    {
        Caption = 'Error';
    }
    value(4; Cancelled)
    {
        Caption = 'Cancelled';
    }
}
