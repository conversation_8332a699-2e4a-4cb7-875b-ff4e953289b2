// Table to store contact details for mass email background jobs
table 50113 "Mass Email Job Contacts"
{
    Caption = 'Mass Email Job Contacts';
    DataClassification = CustomerContent;

    fields
    {
        field(1; "Job Entry No."; Integer)
        {
            Caption = 'Job Entry No.';
            TableRelation = "Mass Email Job Parameters"."Entry No.";
        }
        field(2; "Line No."; Integer)
        {
            Caption = 'Line No.';
        }
        field(3; "Contact No."; Code[20])
        {
            Caption = 'Contact No.';
            TableRelation = Contact;
        }
        field(4; "Contact Name"; Text[100])
        {
            Caption = 'Contact Name';
        }
        field(5; "E-Mail"; Text[80])
        {
            Caption = 'E-Mail';
        }
        field(6; "Company Name"; Text[100])
        {
            Caption = 'Company Name';
        }
        field(7; "Salesperson Code"; Code[20])
        {
            Caption = 'Salesperson Code';
            TableRelation = "Salesperson/Purchaser";
        }
        field(8; "Email Sent"; Boolean)
        {
            Caption = 'Email Sent';
        }
        field(9; "Sent Date Time"; DateTime)
        {
            Caption = 'Sent Date Time';
        }
        field(10; "Error Message"; Text[250])
        {
            Caption = 'Error Message';
        }
    }

    keys
    {
        key(PK; "Job Entry No.", "Line No.")
        {
            Clustered = true;
        }
        key(ContactKey; "Contact No.")
        {
        }
        key(EmailSentKey; "Job Entry No.", "Email Sent")
        {
        }
    }
}
