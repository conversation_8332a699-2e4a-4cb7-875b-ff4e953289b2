// Table to store attachments for mass email background jobs
table 50114 "Mass Email Job Attachments"
{
    Caption = 'Mass Email Job Attachments';
    DataClassification = CustomerContent;

    fields
    {
        field(1; "Job Entry No."; Integer)
        {
            Caption = 'Job Entry No.';
            TableRelation = "Mass Email Job Parameters"."Entry No.";
        }
        field(2; "Line No."; Integer)
        {
            Caption = 'Line No.';
        }
        field(3; "File Name"; Text[250])
        {
            Caption = 'File Name';
        }
        field(4; "MIME Type"; Text[100])
        {
            Caption = 'MIME Type';
        }
        field(5; "File Content"; Blob)
        {
            Caption = 'File Content';
        }
    }

    keys
    {
        key(PK; "Job Entry No.", "Line No.")
        {
            Clustered = true;
        }
    }

    procedure SetFileContent(var TempBlob: Codeunit "Temp Blob")
    var
        InStream: InStream;
        OutStream: OutStream;
    begin
        TempBlob.CreateInStream(InStream);
        "File Content".CreateOutStream(OutStream);
        CopyStream(OutStream, InStream);
    end;

    procedure GetFileContent(var TempBlob: Codeunit "Temp Blob")
    var
        InStream: InStream;
        OutStream: OutStream;
    begin
        CalcFields("File Content");
        "File Content".CreateInStream(InStream);
        TempBlob.CreateOutStream(OutStream);
        CopyStream(OutStream, InStream);
    end;
}
