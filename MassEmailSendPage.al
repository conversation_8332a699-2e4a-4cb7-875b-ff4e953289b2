// Main page for mass email composition and sending
page 50111 "Mass Email Send"
{
    PageType = Card;
    ApplicationArea = All;
    UsageCategory = Tasks;
    Caption = 'Mass Email Send';
    SourceTable = "Mass Email Contact Buffer";
    SourceTableTemporary = true;

    layout
    {
        area(Content)
        {
            group(EmailComposition)
            {
                Caption = 'Email Details';

                field(FromEmailField; FromEmail)
                {
                    ApplicationArea = All;
                    Caption = 'From';
                    Editable = false;
                    ToolTip = 'Your email account that will send the email';
                }

                field(SalespersonField; SalespersonCode)
                {
                    ApplicationArea = All;
                    Caption = 'Salesperson (Recipients)';
                    TableRelation = "Salesperson/Purchaser";
                    ToolTip = 'Salesperson whose contacts will be loaded as BCC recipients';
                    Editable = IsSalespersonEditable;

                    trigger OnValidate()
                    begin
                        LoadContactsForSalesperson();
                    end;
                }

                field(CampaignField; CampaignNo)
                {
                    ApplicationArea = All;
                    Caption = 'Campaign';
                    ToolTip = 'Select campaign to link interactions';
                    TableRelation = Campaign;
                }

                field(SubjectField; EmailSubject)
                {
                    ApplicationArea = All;
                    Caption = 'Subject';
                    ShowMandatory = true;
                    ToolTip = 'Email subject line';
                }

                field(AttachmentField; AttachmentStatusText)
                {
                    ApplicationArea = All;
                    Caption = 'Attachments';
                    Editable = false;
                    ToolTip = 'Shows current attachments - click Attach File to add more or Remove Attachment to remove';
                    Style = Standard;
                }
            }

            group(MessageComposition)
            {
                Caption = 'Email Message';

                field(MessageField; EmailBody)
                {
                    ApplicationArea = All;
                    Caption = '';
                    ShowCaption = false;
                    MultiLine = true;
                    ShowMandatory = true;
                    ToolTip = 'Email message content - supports rich text formatting. For images, use Attach File for best compatibility.';
                    ExtendedDatatype = RichContent;
                }
            }

            group(RecipientsHeader)
            {
                Caption = 'BCC Recipients (Hidden from each other)';
            }

            group(contacts)
            {
                Caption = 'Contacts';

                repeater(ContactListFull)
                {
                    Caption = 'Select Contacts';
                    FreezeColumn = Selected;

                    field(Selected; Rec.Selected)
                    {
                        ApplicationArea = All;
                        Caption = '✓';
                        ToolTip = 'Include this contact as BCC recipient';

                        trigger OnValidate()
                        begin
                            CurrPage.Update();
                        end;
                    }

                    field(ContactName; Rec."Contact Name")
                    {
                        ApplicationArea = All;
                        Caption = 'Contact Name';
                        Editable = false;
                    }

                    field(EmailAddress; Rec."E-Mail")
                    {
                        ApplicationArea = All;
                        Caption = 'Email Address';
                        Editable = false;
                        ExtendedDatatype = EMail;
                    }

                    field(CompanyName; Rec."Company Name")
                    {
                        ApplicationArea = All;
                        Caption = 'Company';
                        Editable = false;
                    }
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(ShowTemplateVariables)
            {
                ApplicationArea = All;
                Caption = 'Show Template Variables';
                Image = Help;
                ToolTip = 'Show available template variables you can use in subject and message';

                trigger OnAction()
                begin
                    ShowAvailableVariables();
                end;
            }

            action(SendMassEmail)
            {
                ApplicationArea = All;
                Caption = 'Send Email (BCC)';
                Image = SendMail;
                ToolTip = 'Send email to selected contacts as BCC (they will not see each other). This will send 1 email to all selected contacts at once using the BCC field. Contacts will not see each other''s email addresses.';

                trigger OnAction()
                begin
                    SendEmail();
                end;
            }

            action(SendIndividualEmails)
            {
                ApplicationArea = All;
                Caption = 'Send Individually (Background)';
                Image = SendMail;
                ToolTip = 'Queue separate emails to each selected contact for background processing. Emails will be sent in the background using job queues. You can monitor progress in the Job Queue Entries page.';

                trigger OnAction()
                begin
                    QueueIndividualEmails();
                end;
            }

            action(AttachFile)
            {
                ApplicationArea = All;
                Caption = 'Attach File';
                Image = Attach;
                ToolTip = 'Add file attachment (can attach multiple files)';

                trigger OnAction()
                begin
                    AttachFileToEmail();
                end;
            }

            action(RemoveAttachment)
            {
                ApplicationArea = All;
                Caption = 'Remove All Attachments';
                Image = Delete;
                Enabled = HasAttachments;
                ToolTip = 'Remove all attachments';

                trigger OnAction()
                begin
                    DetachAllFiles();
                end;
            }

            action(SelectAll)
            {
                ApplicationArea = All;
                Caption = 'Select All';
                Image = SelectEntries;
                ToolTip = 'Select all contacts';

                trigger OnAction()
                begin
                    SelectAllContacts(true);
                end;
            }

            action(ClearAll)
            {
                ApplicationArea = All;
                Caption = 'Clear All';
                Image = ClearFilter;
                ToolTip = 'Deselect all contacts';

                trigger OnAction()
                begin
                    SelectAllContacts(false);
                end;
            }

            action(ImportWordContent)
            {
                ApplicationArea = All;
                Caption = 'Import from Word';
                Image = Attach;
                ToolTip = 'Upload a Microsoft Word (.docx) file and import its full content (including images and formatting) into the Message field.';

                trigger OnAction()
                begin
                    ImportDocxContent();
                end;
            }

            action(ViewMassEmailJobs)
            {
                ApplicationArea = All;
                Caption = 'View Mass Email Jobs';
                Image = Job;
                ToolTip = 'View the status of mass email background jobs';

                trigger OnAction()
                begin
                    PAGE.Run(PAGE::"Mass Email Job List");
                end;
            }
        }

        area(Promoted)
        {
            group(Email)
            {
                Caption = 'Email';
                actionref(SendMassEmailRef; SendMassEmail)
                {
                }
                actionref(SendIndividualEmailsRef; SendIndividualEmails)
                {
                }
                actionref(AttachFileRef; AttachFile)
                {
                }
                actionref(RemoveAttachmentRef; RemoveAttachment)
                {
                }
            }
            group(ContactSelection)
            {
                Caption = 'Contact Selection';
                actionref(SelectAllRef; SelectAll)
                {
                }
                actionref(ClearAllRef; ClearAll)
                {
                }
            }
            group(JobManagement)
            {
                Caption = 'Job Management';
                actionref(ViewMassEmailJobsRef; ViewMassEmailJobs)
                {
                }
            }
        }
    }

    var
        SalespersonCode: Code[20];
        CampaignNo: Code[20];
        FromEmail: Text[80];
        EmailSubject: Text[250];
        EmailBody: Text;
        AttachmentFileNames: List of [Text];
        AttachmentStatusText: Text;
        HasAttachments: Boolean;
        AttachmentTempBlobs: List of [Codeunit "Temp Blob"];
        AttachmentMimeTypes: List of [Text];
        IsSalespersonEditable: Boolean;
        Dialog: Dialog;
        PersonalSubject: Text;
        PersonalBody: Text;
        DocumentReportMgt: Codeunit "Document Report Mgt.";

    trigger OnOpenPage()
    var
        MassEmailMgt: Codeunit "Mass Email Management";
        UserID: Text;
    begin
        UserID := UserId();
        IsSalespersonEditable := (UserID = 'APALACIO') or (UserID = 'RALMARAZ') or (UserID = 'FAITRABD') or (UserID = 'CMBECHARA');

        FromEmail := MassEmailMgt.GetUserEmail();
        SalespersonCode := MassEmailMgt.GetCurrentUserSalespersonCode();

        if SalespersonCode <> '' then
            LoadContactsForSalesperson()
        else if IsSalespersonEditable then
            SalespersonCode := 'MARKETING';

        UpdateAttachmentStatus();
    end;

    local procedure LoadContactsForSalesperson()
    var
        MassEmailMgt: Codeunit "Mass Email Management";
    begin
        MassEmailMgt.LoadContactsForSalesperson(SalespersonCode, Rec);
        CurrPage.Update(false);
    end;

    local procedure UpdateAttachmentStatus()
    var
        FileCount: Integer;
        FileList: Text;
        i: Integer;
    begin
        FileCount := AttachmentFileNames.Count();
        if FileCount > 0 then begin
            HasAttachments := true;
            if FileCount = 1 then
                AttachmentStatusText := StrSubstNo('📎 1 file: %1', AttachmentFileNames.Get(1))
            else begin
                AttachmentStatusText := StrSubstNo('📎 %1 files: ', FileCount);
                for i := 1 to FileCount do begin
                    if i > 1 then
                        FileList += ', ';
                    FileList += AttachmentFileNames.Get(i);
                end;
                AttachmentStatusText += FileList;
            end;
        end else begin
            HasAttachments := false;
            AttachmentStatusText := 'No attachments - click Attach File to add';
        end;
    end;

    local procedure SelectAllContacts(SelectValue: Boolean)
    begin
        Rec.Reset();
        if Rec.FindSet(true) then
            repeat
                Rec.Selected := SelectValue;
                Rec.Modify();
            until Rec.Next() = 0;

        CurrPage.Update(false);
    end;

    local procedure AttachFileToEmail()
    var
        FileName: Text;
        InStream: InStream;
        OutStream: OutStream;
        TempBlob: Codeunit "Temp Blob";
    begin
        if UploadIntoStream('Select file to attach', '', '', FileName, InStream) then begin
            TempBlob.CreateOutStream(OutStream);
            CopyStream(OutStream, InStream);

            AttachmentFileNames.Add(FileName);
            AttachmentTempBlobs.Add(TempBlob);
            AttachmentMimeTypes.Add(GetMimeType(FileName));

            UpdateAttachmentStatus();
        end;
    end;

    local procedure DetachAllFiles()
    begin
        Clear(AttachmentFileNames);
        Clear(AttachmentTempBlobs);
        Clear(AttachmentMimeTypes);
        UpdateAttachmentStatus();
    end;

    local procedure GetMimeType(FileName: Text): Text
    var
        FileExtension: Text;
    begin
        FileExtension := LowerCase(CopyStr(FileName, StrPos(FileName, '.') + 1));

        case FileExtension of
            'pdf':
                exit('application/pdf');
            'doc':
                exit('application/msword');
            'docx':
                exit('application/vnd.openxmlformats-officedocument.wordprocessingml.document');
            'xls':
                exit('application/vnd.ms-excel');
            'xlsx':
                exit('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            'txt':
                exit('text/plain');
            'jpg', 'jpeg':
                exit('image/jpeg');
            'png':
                exit('image/png');
            'gif':
                exit('image/gif');
            else
                exit('application/octet-stream');
        end;
    end;

    local procedure SendEmail()
    var
        MassEmailMgt: Codeunit "Mass Email Management";
        EmailMessage: Codeunit "Email Message";
        Email: Codeunit Email;
        AttachmentInStream: InStream;
        SelectedCount: Integer;
        TempMassEmailContactBuffer: Record "Mass Email Contact Buffer" temporary;
        i: Integer;
        TempBlob: Codeunit "Temp Blob";
    begin
        // Validation
        if SalespersonCode = '' then begin
            Message('Please select a salesperson to load contacts.');
            exit;
        end;

        if EmailSubject = '' then begin
            Message('Please enter an email subject.');
            exit;
        end;

        if EmailBody = '' then begin
            Message('Please enter an email message.');
            exit;
        end;

        if CampaignNo = '' then begin
            Message('Please select a campaign.');
            exit;
        end;

        SelectedCount := MassEmailMgt.GetSelectedContactCount(Rec);
        if SelectedCount = 0 then begin
            Message('Please select at least one contact.');
            exit;
        end;

        // Confirmation
        if not Confirm('Send email to %1 contacts as BCC recipients?\\They will NOT see each other''s email addresses.', false, SelectedCount) then
            exit;

        // Create email with rich HTML content
        EmailMessage.Create('', EmailSubject, EmailBody, true);

        // Add each selected contact as BCC recipient
        TempMassEmailContactBuffer.Copy(Rec, true);
        TempMassEmailContactBuffer.Reset();
        TempMassEmailContactBuffer.SetRange(Selected, true);

        if TempMassEmailContactBuffer.FindSet() then
            repeat
                EmailMessage.AddRecipient(Enum::"Email Recipient Type"::Bcc, TempMassEmailContactBuffer."E-Mail");
            until TempMassEmailContactBuffer.Next() = 0;

        // Add all attachments
        for i := 1 to AttachmentFileNames.Count() do begin
            TempBlob := AttachmentTempBlobs.Get(i);
            TempBlob.CreateInStream(AttachmentInStream);
            EmailMessage.AddAttachment(AttachmentFileNames.Get(i), AttachmentMimeTypes.Get(i), AttachmentInStream);
        end;

        // Send the email
        if Email.Send(EmailMessage) then begin
            LogInteractions(TempMassEmailContactBuffer);
            Message('Mass email sent successfully to %1 BCC recipients with %2 attachments!', SelectedCount, AttachmentFileNames.Count());
            CurrPage.Close();
        end else
            Message('Failed to send email. Please check your email configuration.');
    end;

    local procedure SendEmailsIndividually()
    var
        EmailMessage: Codeunit "Email Message";
        Email: Codeunit Email;
        TempMassEmailContactBuffer: Record "Mass Email Contact Buffer" temporary;
        AttachmentInStream: InStream;
        TempBlob: Codeunit "Temp Blob";
        SelectedCount: Integer;
        SentCount: Integer;
        BatchSize: Integer;
        WaitMilliseconds: Integer;
        i: Integer;
        Dialog: Dialog;
    begin
        // Re-use the same validations as the bulk BCC send
        if SalespersonCode = '' then begin
            Message('Please select a salesperson to load contacts.');
            exit;
        end;

        if EmailSubject = '' then begin
            Message('Please enter an email subject.');
            exit;
        end;

        if EmailBody = '' then begin
            Message('Please enter an email message.');
            exit;
        end;

        if CampaignNo = '' then begin
            Message('Please select a campaign.');
            exit;
        end;

        // Get number of selected contacts
        TempMassEmailContactBuffer.Copy(Rec, true);
        TempMassEmailContactBuffer.SetRange(Selected, true);
        SelectedCount := TempMassEmailContactBuffer.Count();

        if SelectedCount = 0 then begin
            Message('Please select at least one contact.');
            exit;
        end;

        if not Confirm('This will send %1 individual email(s) - one message per recipient - using the TO field instead of BCC.\\Do you want to continue? This can take several minutes to complete as it sends 25 emails at a time with a 60 second pause between each batch to avoid rate limiting.', false, SelectedCount) then
            exit;

        // Throttling settings – Business Central email accounts default to 30 msgs/min.  
        // We add a small safety delay after each batch to respect that limit.
        BatchSize := 25; // Number of emails to send before pausing
        WaitMilliseconds := 60000; // Pause duration (60 seconds)

        SentCount := 0;
        if TempMassEmailContactBuffer.FindSet() then begin
            Dialog.Open('Sending emails... #1###### of #2######', SentCount, SelectedCount);
            repeat
                // Create a personalised subject/body for this recipient by replacing tokens
                PersonalSubject := ReplaceTokens(EmailSubject, TempMassEmailContactBuffer);
                PersonalBody := ReplaceTokens(EmailBody, TempMassEmailContactBuffer);

                EmailMessage.Create('', PersonalSubject, PersonalBody, true);

                EmailMessage.AddRecipient(Enum::"Email Recipient Type"::"To", TempMassEmailContactBuffer."E-Mail");

                // Add all attachments (if any)
                for i := 1 to AttachmentFileNames.Count() do begin
                    TempBlob := AttachmentTempBlobs.Get(i);
                    TempBlob.CreateInStream(AttachmentInStream);
                    EmailMessage.AddAttachment(AttachmentFileNames.Get(i), AttachmentMimeTypes.Get(i), AttachmentInStream);
                end;

                // Send the email – any errors will raise an exception and stop the loop
                Email.Send(EmailMessage);

                SentCount += 1;
                Dialog.Update(1, SentCount);

                // Simple throttle – wait after each batch
                if (SentCount mod BatchSize = 0) and (SentCount < SelectedCount) then
                    Sleep(WaitMilliseconds);

            until TempMassEmailContactBuffer.Next() = 0;
            Dialog.Close();
        end;

        // Log interactions for all recipients
        LogInteractions(TempMassEmailContactBuffer);

        Message('Successfully queued %1 individual email(s) for sending!', SentCount);
        CurrPage.Close();
    end;

    /// <summary>
    /// Replaces placeholder tokens (e.g. %ContactName%) in the supplied template text with
    /// values taken from the provided Mass Email Contact Buffer record.
    /// Supported tokens:
    ///   %ContactName%       – Contact Name
    ///   %ContactFirstName%  – Contact First Name
    ///   %CompanyName%       – Company Name
    ///   %Email%            – E-Mail address
    ///   %SalespersonCode%  – Salesperson Code
    /// Additional tokens can be added by extending this method.
    /// </summary>
    /// <param name="Template">The original text that may contain tokens.</param>
    /// <param name="ContactBuf">The contact whose data will replace the tokens.</param>
    /// <returns>The resulting text with all recognised tokens replaced.</returns>
    local procedure ReplaceTokens(Template: Text; ContactBuf: Record "Mass Email Contact Buffer"): Text
    var
        ResultText: Text;
        ContactFirstName: Text;
    begin
        ResultText := Template;

        // Extract first name from contact name
        ContactFirstName := ExtractFirstName(ContactBuf."Contact Name");

        ResultText := TextReplace(ResultText, '%ContactName%', ContactBuf."Contact Name");
        ResultText := TextReplace(ResultText, '%ContactFirstName%', ContactFirstName);
        ResultText := TextReplace(ResultText, '%CompanyName%', ContactBuf."Company Name");
        ResultText := TextReplace(ResultText, '%Email%', ContactBuf."E-Mail");
        ResultText := TextReplace(ResultText, '%SalespersonCode%', ContactBuf."Salesperson Code");

        exit(ResultText);
    end;

    /// <summary>
    /// Extracts the first name from a full contact name.
    /// </summary>
    /// <param name="FullName">The full contact name.</param>
    /// <returns>The first name (everything before the first space).</returns>
    local procedure ExtractFirstName(FullName: Text): Text
    var
        SpacePos: Integer;
    begin
        SpacePos := StrPos(FullName, ' ');
        if SpacePos > 0 then
            exit(CopyStr(FullName, 1, SpacePos - 1))
        else
            exit(FullName);
    end;

    /// <summary>
    /// Simple helper that replaces all occurrences of <paramref name="Token"/> in <paramref name="Source"/>
    /// with <paramref name="Replacement"/>. Implemented without .NET to stay SaaS-compatible.
    /// </summary>
    local procedure TextReplace(Source: Text; Token: Text; Replacement: Text): Text
    var
        Pos: Integer;
    begin
        Pos := StrPos(Source, Token);
        while Pos > 0 do begin
            Source := CopyStr(Source, 1, Pos - 1) + Replacement + CopyStr(Source, Pos + StrLen(Token));
            Pos := StrPos(Source, Token);
        end;

        exit(Source);
    end;

    local procedure ShowAvailableVariables()
    var
        VariablesText: Text;
    begin
        VariablesText := 'Available Template Variables:' + '\' + '\';
        VariablesText += '%ContactName% - Contact Name' + '\';
        VariablesText += '%ContactFirstName% - Contact First Name' + '\';
        VariablesText += '%CompanyName% - Company Name' + '\';
        VariablesText += '%Email% - E-Mail address' + '\';
        VariablesText += '%SalespersonCode% - Salesperson Code' + '\' + '\';
        VariablesText += 'You can use these variables in both the Subject and Message fields.';

        Message(VariablesText);
    end;

    local procedure ImportDocxContent()
    var
        FileName: Text;
        InStr: InStream;
        OutStr: OutStream;
        TempBlob: Codeunit "Temp Blob";
        HtmlStr: Text;
    begin
        // Prompt user to select a Word document
        if not UploadIntoStream('Select Word document to import', '', 'Word Documents|*.docx', FileName, InStr) then
            exit;

        // Copy the uploaded document into a TempBlob so we can pass it to the converter
        TempBlob.CreateOutStream(OutStr);
        CopyStream(OutStr, InStr);

        // Convert the DOCX to HTML – this keeps formatting and embeds images as base64 data URIs
        DocumentReportMgt.ConvertWordToHtml(TempBlob);

        // Read the resulting HTML back into a text variable using UTF-8 to preserve special characters
        TempBlob.CreateInStream(InStr, TEXTENCODING::UTF8);
        InStr.ReadText(HtmlStr);

        // Set the EmailBody and refresh the page so the rich-text editor shows the imported content
        EmailBody := HtmlStr;
        CurrPage.Update(false);
    end;

    local procedure QueueIndividualEmails()
    var
        MassEmailJobParams: Record "Mass Email Job Parameters";
        MassEmailJobContacts: Record "Mass Email Job Contacts";
        MassEmailJobAttachments: Record "Mass Email Job Attachments";
        TempMassEmailContactBuffer: Record "Mass Email Contact Buffer" temporary;
        JobQueueEntry: Record "Job Queue Entry";
        TempBlob: Codeunit "Temp Blob";
        SelectedCount: Integer;
        LineNo: Integer;
        i: Integer;
    begin
        // Re-use the same validations as the bulk BCC send
        if SalespersonCode = '' then begin
            Message('Please select a salesperson to load contacts.');
            exit;
        end;

        if EmailSubject = '' then begin
            Message('Please enter an email subject.');
            exit;
        end;

        if EmailBody = '' then begin
            Message('Please enter an email message.');
            exit;
        end;

        if CampaignNo = '' then begin
            Message('Please select a campaign.');
            exit;
        end;

        // Get number of selected contacts
        TempMassEmailContactBuffer.Copy(Rec, true);
        TempMassEmailContactBuffer.SetRange(Selected, true);
        SelectedCount := TempMassEmailContactBuffer.Count();

        if SelectedCount = 0 then begin
            Message('Please select at least one contact.');
            exit;
        end;

        if not Confirm('This will queue %1 individual email(s) for background processing.\\The emails will be sent in the background using job queues. You can monitor progress in the Job Queue Entries page.\\Do you want to continue?', false, SelectedCount) then
            exit;

        // Create job parameters record
        MassEmailJobParams.Init();
        MassEmailJobParams."Salesperson Code" := SalespersonCode;
        MassEmailJobParams."Campaign No." := CampaignNo;
        MassEmailJobParams."Email Subject" := EmailSubject;
        MassEmailJobParams.SetEmailBody(EmailBody);
        MassEmailJobParams."Created By" := UserId();
        MassEmailJobParams."Created Date Time" := CurrentDateTime();
        MassEmailJobParams."Total Recipients" := SelectedCount;
        MassEmailJobParams."Emails Sent" := 0;
        MassEmailJobParams.Status := MassEmailJobParams.Status::Queued;
        MassEmailJobParams."Attachment Count" := AttachmentFileNames.Count();
        MassEmailJobParams.Insert(true);

        // Store selected contacts
        LineNo := 0;
        if TempMassEmailContactBuffer.FindSet() then
            repeat
                LineNo += 10000;
                MassEmailJobContacts.Init();
                MassEmailJobContacts."Job Entry No." := MassEmailJobParams."Entry No.";
                MassEmailJobContacts."Line No." := LineNo;
                MassEmailJobContacts."Contact No." := TempMassEmailContactBuffer."Contact No.";
                MassEmailJobContacts."Contact Name" := TempMassEmailContactBuffer."Contact Name";
                MassEmailJobContacts."E-Mail" := TempMassEmailContactBuffer."E-Mail";
                MassEmailJobContacts."Company Name" := TempMassEmailContactBuffer."Company Name";
                MassEmailJobContacts."Salesperson Code" := TempMassEmailContactBuffer."Salesperson Code";
                MassEmailJobContacts."Email Sent" := false;
                MassEmailJobContacts.Insert();
            until TempMassEmailContactBuffer.Next() = 0;

        // Store attachments
        LineNo := 0;
        for i := 1 to AttachmentFileNames.Count() do begin
            LineNo += 10000;
            MassEmailJobAttachments.Init();
            MassEmailJobAttachments."Job Entry No." := MassEmailJobParams."Entry No.";
            MassEmailJobAttachments."Line No." := LineNo;
            MassEmailJobAttachments."File Name" := AttachmentFileNames.Get(i);
            MassEmailJobAttachments."MIME Type" := AttachmentMimeTypes.Get(i);
            TempBlob := AttachmentTempBlobs.Get(i);
            MassEmailJobAttachments.SetFileContent(TempBlob);
            MassEmailJobAttachments.Insert();
        end;

        // Create and enqueue job queue entry
        JobQueueEntry.Init();
        JobQueueEntry."Object Type to Run" := JobQueueEntry."Object Type to Run"::Codeunit;
        JobQueueEntry."Object ID to Run" := CODEUNIT::"Mass Email Bg Processor";
        JobQueueEntry."Parameter String" := Format(MassEmailJobParams."Entry No.");
        JobQueueEntry.Description := StrSubstNo('Mass Email Job - %1 recipients', SelectedCount);
        JobQueueEntry."User ID" := UserId();
        JobQueueEntry."Earliest Start Date/Time" := CurrentDateTime();
        JobQueueEntry."Maximum No. of Attempts to Run" := 3;
        JobQueueEntry.Status := JobQueueEntry.Status::Ready;
        JobQueueEntry.Insert(true);

        // Update job parameters with Job Queue Entry ID
        MassEmailJobParams."Job Queue Entry ID" := JobQueueEntry.ID;
        MassEmailJobParams.Modify();

        Message('Mass email job has been queued successfully!\\Job Queue Entry ID: %1\\You can monitor progress in the Job Queue Entries page.', JobQueueEntry.ID);
        CurrPage.Close();
    end;

    local procedure LogInteractions(var SelectedContacts: Record "Mass Email Contact Buffer" temporary)
    var
        ContactRec: Record Contact;
        InteractionLogEntry: Record "Interaction Log Entry";
        CompanyContact: Record Contact;
        CompanyName: Text[100];
        CompanyNo: Code[20];
    begin
        if CampaignNo = '' then
            exit;

        SelectedContacts.Reset();
        SelectedContacts.SetRange(Selected, true);

        if SelectedContacts.FindSet() then
            repeat
                if ContactRec.Get(SelectedContacts."Contact No.") then begin
                    Clear(InteractionLogEntry);
                    if InteractionLogEntry.FindLast() then
                        InteractionLogEntry."Entry No." := InteractionLogEntry."Entry No." + 1
                    else
                        InteractionLogEntry."Entry No." := 1;

                    InteractionLogEntry.Init();
                    InteractionLogEntry."Contact No." := ContactRec."No.";
                    InteractionLogEntry."Contact Name" := ContactRec.Name;

                    // Determine company info
                    CompanyNo := '';
                    CompanyName := '';
                    if ContactRec."Company No." <> '' then begin
                        CompanyNo := ContactRec."Company No.";
                        if CompanyContact.Get(CompanyNo) then
                            CompanyName := CompanyContact.Name;
                    end;

                    if CompanyName = '' then
                        CompanyName := ContactRec."Company Name";
                    if CompanyName = '' then
                        CompanyName := ContactRec.Name;

                    InteractionLogEntry."Contact Company No." := CompanyNo;
                    InteractionLogEntry."Contact Company Name" := CompanyName;
                    InteractionLogEntry."Salesperson Code" := ContactRec."Salesperson Code";
                    InteractionLogEntry."Interaction Template Code" := 'EMAIL-SENT';
                    InteractionLogEntry."Date" := Today();
                    InteractionLogEntry."Time of Interaction" := Time();
                    InteractionLogEntry."Campaign No." := CampaignNo;
                    InteractionLogEntry."Campaign Entry No." := 0;
                    InteractionLogEntry.Description := 'Mass email sent via Mass Email Send page';
                    InteractionLogEntry.Insert(true);
                end;
            until SelectedContacts.Next() = 0;
    end;
}
