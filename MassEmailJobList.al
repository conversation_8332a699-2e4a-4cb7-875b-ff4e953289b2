// Page to view mass email job status and progress
page 50115 "Mass Email Job List"
{
    PageType = List;
    ApplicationArea = All;
    UsageCategory = Lists;
    Caption = 'Mass Email Jobs';
    SourceTable = "Mass Email Job Parameters";
    Editable = false;

    layout
    {
        area(Content)
        {
            repeater(JobList)
            {
                field("Entry No."; Rec."Entry No.")
                {
                    ApplicationArea = All;
                    Caption = 'Entry No.';
                }
                field("Created Date Time"; Rec."Created Date Time")
                {
                    ApplicationArea = All;
                    Caption = 'Created';
                }
                field("Created By"; Rec."Created By")
                {
                    ApplicationArea = All;
                    Caption = 'Created By';
                }
                field("Salesperson Code"; Rec."Salesperson Code")
                {
                    ApplicationArea = All;
                    Caption = 'Salesperson';
                }
                field("Campaign No."; Rec."Campaign No.")
                {
                    ApplicationArea = All;
                    Caption = 'Campaign';
                }
                field("Email Subject"; Rec."Email Subject")
                {
                    ApplicationArea = All;
                    Caption = 'Subject';
                }
                field("Total Recipients"; Rec."Total Recipients")
                {
                    ApplicationArea = All;
                    Caption = 'Total Recipients';
                }
                field("Emails Sent"; Rec."Emails Sent")
                {
                    ApplicationArea = All;
                    Caption = 'Emails Sent';
                    Style = Favorable;
                }
                field(Progress; GetProgressText())
                {
                    ApplicationArea = All;
                    Caption = 'Progress';
                }
                field(Status; Rec.Status)
                {
                    ApplicationArea = All;
                    Caption = 'Status';
                    StyleExpr = GetStatusStyle();
                }
                field("Attachment Count"; Rec."Attachment Count")
                {
                    ApplicationArea = All;
                    Caption = 'Attachments';
                }
                field("Error Message"; Rec."Error Message")
                {
                    ApplicationArea = All;
                    Caption = 'Error Message';
                    Style = Unfavorable;
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(ViewContacts)
            {
                ApplicationArea = All;
                Caption = 'View Contacts';
                Image = ContactPerson;
                ToolTip = 'View the contacts for this mass email job';

                trigger OnAction()
                var
                    MassEmailJobContacts: Record "Mass Email Job Contacts";
                    MassEmailJobContactsPage: Page "Mass Email Job Contacts";
                begin
                    MassEmailJobContacts.SetRange("Job Entry No.", Rec."Entry No.");
                    MassEmailJobContactsPage.SetTableView(MassEmailJobContacts);
                    MassEmailJobContactsPage.RunModal();
                end;
            }

            action(ViewJobQueueEntry)
            {
                ApplicationArea = All;
                Caption = 'View Job Queue Entry';
                Image = Job;
                ToolTip = 'View the related job queue entry';

                trigger OnAction()
                var
                    JobQueueEntry: Record "Job Queue Entry";
                begin
                    if JobQueueEntry.Get(Rec."Job Queue Entry ID") then
                        PAGE.Run(PAGE::"Job Queue Entries", JobQueueEntry)
                    else
                        Message('Job Queue Entry not found.');
                end;
            }

            action(Refresh)
            {
                ApplicationArea = All;
                Caption = 'Refresh';
                Image = Refresh;
                ToolTip = 'Refresh the page to see latest status';

                trigger OnAction()
                begin
                    CurrPage.Update(false);
                end;
            }
        }

        area(Promoted)
        {
            group(Category_Process)
            {
                Caption = 'Actions';
                actionref(ViewContactsRef; ViewContacts)
                {
                }
                actionref(ViewJobQueueEntryRef; ViewJobQueueEntry)
                {
                }
                actionref(RefreshRef; Refresh)
                {
                }
            }
        }
    }

    local procedure GetProgressText(): Text
    begin
        if Rec."Total Recipients" = 0 then
            exit('0%');

        exit(StrSubstNo('%1%', Round(Rec."Emails Sent" * 100 / Rec."Total Recipients", 1)));
    end;

    local procedure GetStatusStyle(): Text
    begin
        case Rec.Status of
            Rec.Status::Queued:
                exit('Standard');
            Rec.Status::"In Progress":
                exit('StandardAccent');
            Rec.Status::Completed:
                exit('Favorable');
            Rec.Status::Error:
                exit('Unfavorable');
            Rec.Status::Cancelled:
                exit('Subordinate');
            else
                exit('Standard');
        end;
    end;
}

    local procedure GetProgressText(): Text
    begin
        if Rec."Total Recipients" = 0 then
            exit('0%');
        
        exit(StrSubstNo('%1%', Round(Rec."Emails Sent" * 100 / Rec."Total Recipients", 1)));
    end;

    local procedure GetStatusStyle(): Text
    begin
        case Rec.Status of
            Rec.Status::Queued:
                exit('Standard');
            Rec.Status::"In Progress":
                exit('StandardAccent');
            Rec.Status::Completed:
                exit('Favorable');
            Rec.Status::Error:
                exit('Unfavorable');
            Rec.Status::Cancelled:
                exit('Subordinate');
            else
                exit('Standard');
        end;
    end;
}
