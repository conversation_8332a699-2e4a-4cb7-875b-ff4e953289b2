// Codeunit for processing mass emails in the background using job queues
codeunit 50112 "Mass Email Bg Processor"
{
    TableNo = "Job Queue Entry";

    trigger OnRun()
    var
        MassEmailJobParams: Record "Mass Email Job Parameters";
        JobEntryNo: Integer;
    begin
        // Get the job entry number from the job queue parameter string
        if not Evaluate(JobEntryNo, Rec."Parameter String") then
            Error('Invalid parameter string in Job Queue Entry. Expected Mass Email Job Entry No.');

        // Find the job parameters record using the Entry No.
        if MassEmailJobParams.Get(JobEntryNo) then
            ProcessMassEmailJob(MassEmailJobParams)
        else
            Error('Mass email job parameters not found for Entry No.: %1', JobEntryNo);
    end;

    local procedure ProcessMassEmailJob(var MassEmailJobParams: Record "Mass Email Job Parameters")
    var
        MassEmailJobContacts: Record "Mass Email Job Contacts";
        MassEmailJobAttachments: Record "Mass Email Job Attachments";
        EmailMessage: Codeunit "Email Message";
        Email: Codeunit Email;
        TempBlob: Codeunit "Temp Blob";
        AttachmentInStream: InStream;
        EmailSubject: Text;
        EmailBody: Text;
        SentCount: Integer;
        BatchSize: Integer;
        WaitMilliseconds: Integer;
        i: Integer;
    begin
        // Update status to In Progress
        MassEmailJobParams.Status := MassEmailJobParams.Status::"In Progress";
        MassEmailJobParams.Modify();
        Commit();

        // Get email content
        EmailSubject := MassEmailJobParams."Email Subject";
        EmailBody := MassEmailJobParams.GetEmailBody();

        // Throttling settings
        BatchSize := 25; // Number of emails to send before pausing
        WaitMilliseconds := 60000; // Pause duration (60 seconds)

        SentCount := 0;

        // Process each contact
        MassEmailJobContacts.SetRange("Job Entry No.", MassEmailJobParams."Entry No.");
        MassEmailJobContacts.SetRange("Email Sent", false);

        if MassEmailJobContacts.FindSet(true) then
            repeat
                if ProcessSingleEmail(MassEmailJobContacts, MassEmailJobParams, EmailSubject, EmailBody) then begin
                    SentCount += 1;
                    MassEmailJobParams."Emails Sent" := SentCount;
                    MassEmailJobParams.Modify();
                    Commit();

                    // Simple throttle – wait after each batch
                    if (SentCount mod BatchSize = 0) then
                        Sleep(WaitMilliseconds);
                end;
            until MassEmailJobContacts.Next() = 0;

        // Update final status
        if SentCount = MassEmailJobParams."Total Recipients" then begin
            MassEmailJobParams.Status := MassEmailJobParams.Status::Completed;
            MassEmailJobParams."Error Message" := '';
        end else begin
            MassEmailJobParams.Status := MassEmailJobParams.Status::Error;
            MassEmailJobParams."Error Message" := StrSubstNo('Only %1 of %2 emails were sent successfully', SentCount, MassEmailJobParams."Total Recipients");
        end;

        MassEmailJobParams.Modify();
        Commit();

        // Log interactions for all successfully sent emails
        LogInteractionsForJob(MassEmailJobParams);
    end;

    local procedure ProcessSingleEmail(var MassEmailJobContact: Record "Mass Email Job Contacts"; var MassEmailJobParams: Record "Mass Email Job Parameters"; EmailSubject: Text; EmailBody: Text): Boolean
    var
        EmailMessage: Codeunit "Email Message";
        Email: Codeunit Email;
        MassEmailJobAttachments: Record "Mass Email Job Attachments";
        TempBlob: Codeunit "Temp Blob";
        AttachmentInStream: InStream;
        PersonalSubject: Text;
        PersonalBody: Text;
    begin
        // Create personalized subject/body for this recipient by replacing tokens
        PersonalSubject := ReplaceTokens(EmailSubject, MassEmailJobContact);
        PersonalBody := ReplaceTokens(EmailBody, MassEmailJobContact);

        EmailMessage.Create('', PersonalSubject, PersonalBody, true);
        EmailMessage.AddRecipient(Enum::"Email Recipient Type"::"To", MassEmailJobContact."E-Mail");

        // Add all attachments
        MassEmailJobAttachments.SetRange("Job Entry No.", MassEmailJobParams."Entry No.");
        if MassEmailJobAttachments.FindSet() then
            repeat
                MassEmailJobAttachments.GetFileContent(TempBlob);
                TempBlob.CreateInStream(AttachmentInStream);
                EmailMessage.AddAttachment(MassEmailJobAttachments."File Name", MassEmailJobAttachments."MIME Type", AttachmentInStream);
            until MassEmailJobAttachments.Next() = 0;

        // Send the email
        if Email.Send(EmailMessage) then begin
            MassEmailJobContact."Email Sent" := true;
            MassEmailJobContact."Sent Date Time" := CurrentDateTime();
            MassEmailJobContact."Error Message" := '';
            MassEmailJobContact.Modify();
            exit(true);
        end else begin
            MassEmailJobContact."Email Sent" := false;
            MassEmailJobContact."Error Message" := 'Failed to send email';
            MassEmailJobContact.Modify();
            exit(false);
        end;
    end;

    local procedure ReplaceTokens(Template: Text; ContactRec: Record "Mass Email Job Contacts"): Text
    var
        ResultText: Text;
        ContactFirstName: Text;
    begin
        ResultText := Template;

        // Extract first name from contact name
        ContactFirstName := ExtractFirstName(ContactRec."Contact Name");

        ResultText := TextReplace(ResultText, '%ContactName%', ContactRec."Contact Name");
        ResultText := TextReplace(ResultText, '%ContactFirstName%', ContactFirstName);
        ResultText := TextReplace(ResultText, '%CompanyName%', ContactRec."Company Name");
        ResultText := TextReplace(ResultText, '%Email%', ContactRec."E-Mail");
        ResultText := TextReplace(ResultText, '%SalespersonCode%', ContactRec."Salesperson Code");

        exit(ResultText);
    end;

    local procedure ExtractFirstName(FullName: Text): Text
    var
        SpacePos: Integer;
    begin
        SpacePos := StrPos(FullName, ' ');
        if SpacePos > 0 then
            exit(CopyStr(FullName, 1, SpacePos - 1))
        else
            exit(FullName);
    end;

    local procedure TextReplace(Source: Text; Token: Text; Replacement: Text): Text
    var
        Pos: Integer;
    begin
        Pos := StrPos(Source, Token);
        while Pos > 0 do begin
            Source := CopyStr(Source, 1, Pos - 1) + Replacement + CopyStr(Source, Pos + StrLen(Token));
            Pos := StrPos(Source, Token);
        end;

        exit(Source);
    end;

    local procedure LogInteractionsForJob(var MassEmailJobParams: Record "Mass Email Job Parameters")
    var
        MassEmailJobContacts: Record "Mass Email Job Contacts";
        ContactRec: Record Contact;
        InteractionLogEntry: Record "Interaction Log Entry";
        CompanyContact: Record Contact;
        CompanyName: Text[100];
        CompanyNo: Code[20];
    begin
        if MassEmailJobParams."Campaign No." = '' then
            exit;

        MassEmailJobContacts.SetRange("Job Entry No.", MassEmailJobParams."Entry No.");
        MassEmailJobContacts.SetRange("Email Sent", true);

        if MassEmailJobContacts.FindSet() then
            repeat
                if ContactRec.Get(MassEmailJobContacts."Contact No.") then begin
                    Clear(InteractionLogEntry);
                    if InteractionLogEntry.FindLast() then
                        InteractionLogEntry."Entry No." := InteractionLogEntry."Entry No." + 1
                    else
                        InteractionLogEntry."Entry No." := 1;

                    InteractionLogEntry.Init();
                    InteractionLogEntry."Contact No." := ContactRec."No.";
                    InteractionLogEntry."Contact Name" := ContactRec.Name;

                    // Determine company info
                    CompanyNo := '';
                    CompanyName := '';
                    if ContactRec."Company No." <> '' then begin
                        CompanyNo := ContactRec."Company No.";
                        if CompanyContact.Get(CompanyNo) then
                            CompanyName := CompanyContact.Name;
                    end;

                    if CompanyName = '' then
                        CompanyName := ContactRec."Company Name";
                    if CompanyName = '' then
                        CompanyName := ContactRec.Name;

                    InteractionLogEntry."Contact Company No." := CompanyNo;
                    InteractionLogEntry."Contact Company Name" := CompanyName;
                    InteractionLogEntry."Salesperson Code" := ContactRec."Salesperson Code";
                    InteractionLogEntry."Interaction Template Code" := 'EMAIL-SENT';
                    InteractionLogEntry."Date" := DT2Date(MassEmailJobContacts."Sent Date Time");
                    InteractionLogEntry."Time of Interaction" := DT2Time(MassEmailJobContacts."Sent Date Time");
                    InteractionLogEntry."Campaign No." := MassEmailJobParams."Campaign No.";
                    InteractionLogEntry."Campaign Entry No." := 0;
                    InteractionLogEntry.Description := 'Mass email sent via background job';
                    InteractionLogEntry.Insert(true);
                end;
            until MassEmailJobContacts.Next() = 0;
    end;
}
