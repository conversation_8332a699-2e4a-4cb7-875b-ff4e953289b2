// Page to view contacts for a specific mass email job
page 50113 "Mass Email Job Contacts"
{
    PageType = List;
    ApplicationArea = All;
    Caption = 'Mass Email Job Contacts';
    SourceTable = "Mass Email Job Contacts";
    Editable = false;

    layout
    {
        area(Content)
        {
            repeater(ContactList)
            {
                field("Contact No."; Rec."Contact No.")
                {
                    ApplicationArea = All;
                    Caption = 'Contact No.';
                }
                field("Contact Name"; Rec."Contact Name")
                {
                    ApplicationArea = All;
                    Caption = 'Contact Name';
                }
                field("E-Mail"; Rec."E-Mail")
                {
                    ApplicationArea = All;
                    Caption = 'Email Address';
                    ExtendedDatatype = EMail;
                }
                field("Company Name"; Rec."Company Name")
                {
                    ApplicationArea = All;
                    Caption = 'Company';
                }
                field("Salesperson Code"; Rec."Salesperson Code")
                {
                    ApplicationArea = All;
                    Caption = 'Salesperson';
                }
                field("Email Sent"; Rec."Email Sent")
                {
                    ApplicationArea = All;
                    Caption = 'Email Sent';
                }
                field("Sent Date Time"; Rec."Sent Date Time")
                {
                    ApplicationArea = All;
                    Caption = 'Sent Date Time';
                }
                field("Error Message"; Rec."Error Message")
                {
                    ApplicationArea = All;
                    Caption = 'Error Message';
                    Style = Unfavorable;
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(ShowSentOnly)
            {
                ApplicationArea = All;
                Caption = 'Show Sent Only';
                Image = Filter;
                ToolTip = 'Filter to show only contacts where email was sent successfully';

                trigger OnAction()
                begin
                    Rec.SetRange("Email Sent", true);
                    CurrPage.Update(false);
                end;
            }

            action(ShowFailedOnly)
            {
                ApplicationArea = All;
                Caption = 'Show Failed Only';
                Image = Filter;
                ToolTip = 'Filter to show only contacts where email failed to send';

                trigger OnAction()
                begin
                    Rec.SetRange("Email Sent", false);
                    CurrPage.Update(false);
                end;
            }

            action(ShowAll)
            {
                ApplicationArea = All;
                Caption = 'Show All';
                Image = ClearFilter;
                ToolTip = 'Clear filters to show all contacts';

                trigger OnAction()
                begin
                    Rec.SetRange("Email Sent");
                    CurrPage.Update(false);
                end;
            }
        }

        area(Promoted)
        {
            group(Filters)
            {
                Caption = 'Filters';
                actionref(ShowSentOnlyRef; ShowSentOnly)
                {
                }
                actionref(ShowFailedOnlyRef; ShowFailedOnly)
                {
                }
                actionref(ShowAllRef; ShowAll)
                {
                }
            }
        }
    }

    local procedure GetEmailSentStyle(): Text
    begin
        if Rec."Email Sent" then
            exit('Favorable')
        else
            exit('Unfavorable');
    end;
}
